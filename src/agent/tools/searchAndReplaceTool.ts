// Core Node.js imports
import path from 'path';
import fs from 'fs/promises';
import delay from 'delay';

// Internal imports
import { AgentManager } from '../agent';
import { SayTool } from '../types/type';
import { HandleError, PushToolResult, RemoveClosingTag, ToolUse, ReportToolAction } from '../types/message';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';

/**
 * Tool for performing search and replace operations on files
 * Supports regex and case-sensitive/insensitive matching
 */

/**
 * Interface for a single replacement operation
 */
interface ReplacementOperation {
  search: string;
  replace: string;
  start_line?: number;
  end_line?: number;
}

/**
 * Validates required parameters for search and replace operation
 */
async function validateParams(
  agent: AgentManager,
  relPath: string | undefined,
  replacements: ReplacementOperation[] | undefined,
  pushToolResult: PushToolResult
): Promise<boolean> {
  if (!relPath) {
    agent.consecutiveMistakeCount++;
    pushToolResult(await agent.sayAndCreateMissingParamError('search_and_replace', 'path'));
    return false;
  }

  // Check if we have either replacements array or individual search/replace params
  if (!replacements) {
    agent.consecutiveMistakeCount++;
    const errorMessage = 'Either "replacements" parameter (JSON array) or both "search" and "replace" parameters must be provided.';
    await agent.say('error', errorMessage);
    pushToolResult(errorMessage);
    return false;
  }

  // Validate replacements array if provided
  if (replacements) {
    if (!Array.isArray(replacements) || replacements.length === 0) {
      agent.consecutiveMistakeCount++;
      const errorMessage = 'The "replacements" parameter must be a non-empty JSON array.';
      pushToolResult(errorMessage);
      return false;
    }

    for (let i = 0; i < replacements.length; i++) {
      const replacement = replacements[i];
      if (!replacement.search || replacement.replace === undefined) {
        agent.consecutiveMistakeCount++;
        const errorMessage = `Replacement operation at index ${i} is missing required "search" or "replace" field.`;
        pushToolResult(errorMessage);
        return false;
      }
    }
  }

  return true;
}

/**
 * Validates that replacement operations don't have overlapping line ranges
 */
function validateNoOverlappingRanges(operations: ReplacementOperation[]): { valid: boolean; error?: string } {
  const rangeOperations = operations.filter(op => op.start_line !== undefined || op.end_line !== undefined);

  for (let i = 0; i < rangeOperations.length; i++) {
    for (let j = i + 1; j < rangeOperations.length; j++) {
      const op1 = rangeOperations[i];
      const op2 = rangeOperations[j];

      const start1 = op1.start_line ?? 1;
      const end1 = op1.end_line ?? Number.MAX_SAFE_INTEGER;
      const start2 = op2.start_line ?? 1;
      const end2 = op2.end_line ?? Number.MAX_SAFE_INTEGER;

      // Check for overlap
      if (!(end1 < start2 || end2 < start1)) {
        return {
          valid: false,
          error: `Overlapping line ranges detected: operation ${i + 1} (lines ${start1}-${end1}) overlaps with operation ${j + 1} (lines ${start2}-${end2})`
        };
      }
    }
  }

  return { valid: true };
}

/**
 * Performs a single search and replace operation on content
 */
function performSingleReplacement(
  content: string,
  operation: ReplacementOperation
): string {
  const { search, replace, start_line, end_line } = operation;

  // Create search pattern and perform replacement
  const flags = 'g';
  const searchPattern = new RegExp(escapeRegExp(search), flags);

  if (start_line !== undefined || end_line !== undefined) {
    // Handle line-specific replacement
    const lines = content.split('\n');
    const start = Math.max((start_line ?? 1) - 1, 0);
    const end = Math.min((end_line ?? lines.length) - 1, lines.length - 1);

    // Get content before and after target section
    const beforeLines = lines.slice(0, start);
    const afterLines = lines.slice(end + 1);

    // Get and modify target section
    const targetContent = lines.slice(start, end + 1).join('\n');
    const modifiedContent = targetContent.replace(searchPattern, replace);
    const modifiedLines = modifiedContent.split('\n');

    // Reconstruct full content
    return [...beforeLines, ...modifiedLines, ...afterLines].join('\n');
  } else {
    // Global replacement
    return content.replace(searchPattern, replace);
  }
}

/**
 * Performs search and replace operations on a file
 * @param agent - Cline instance
 * @param block - Tool use parameters
 * @param handleError - Function to handle errors
 * @param pushToolResult - Function to push tool results
 * @param removeClosingTag - Function to remove closing tags
 */
export async function searchAndReplaceTool(
  agent: AgentManager,
  block: ToolUse,
  handleError: HandleError,
  pushToolResult: PushToolResult,
  removeClosingTag: RemoveClosingTag,
  reportToolAction: ReportToolAction
): Promise<void> {
  // Extract and validate parameters
  const relPath: string | undefined = block.params.path;
  const replacementsParam: string | undefined = block.params.replacements;
  const startLine: number | undefined = block.params.start_line ? parseInt(block.params.start_line, 10) : undefined;
  const endLine: number | undefined = block.params.end_line ? parseInt(block.params.end_line, 10) : undefined;

  try {
    const sharedMessageProps: SayTool = {
      tool: 'editFile',
      path: getReadablePath(agent.cwd, relPath),
      // TODO: 可优化
      content: '',
      tool_version: 'v2'
    };
    // Handle partial tool use
    if (block.partial) {
      agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
      await agent.say('tool', JSON.stringify(sharedMessageProps), block.partial).catch(() => {});
      return;
    }

    // Parse replacements JSON if provided
    let replacements: ReplacementOperation[] | undefined;
    if (replacementsParam) {
      try {
        replacements = JSON.parse(replacementsParam);
      } catch (error) {
        agent.consecutiveMistakeCount++;
        const errorMessage = `Invalid JSON format in "replacements" parameter: ${
          error instanceof Error ? error.message : String(error)
        }`;
        pushToolResult(errorMessage);
        return;
      }
    }

    // Validate required parameters
    if (!(await validateParams(agent, relPath, replacements, pushToolResult))) {
      return;
    }

    // At this point we know relPath is defined
    const validRelPath = relPath as string;
    const operations = replacements as ReplacementOperation[];

    agent.logger.reportUserAction({
      key: 'agent_tools_request',
      type: 'search_and_replace'
    });
    const startToolTime = Date.now();

    const absolutePath = path.resolve(agent.cwd, validRelPath);
    const fileExists = await fileExistsAtPath(absolutePath);

    if (!fileExists) {
      agent.consecutiveMistakeCount++;
      const formattedError = `File does not exist at path: ${absolutePath}\nThe specified file could not be found. Please verify the file path and try again.`;
      await agent.say('error', formattedError);
      pushToolResult(formattedError);
      return;
    }

    // Reset consecutive mistakes since all validations passed
    agent.consecutiveMistakeCount = 0;

    // Read and process file content
    let fileContent: string;
    try {
      fileContent = await fs.readFile(absolutePath, 'utf-8');
    } catch (error) {
      agent.consecutiveMistakeCount++;
      const errorMessage = `Error reading file: ${absolutePath}\nFailed to read the file content: ${
        error instanceof Error ? error.message : String(error)
      }\nPlease verify file permissions and try again.`;
      await agent.say('error', errorMessage);
      pushToolResult(errorMessage);
      return;
    }

    // Validate that operations don't have overlapping line ranges
    const overlapValidation = validateNoOverlappingRanges(operations);
    if (!overlapValidation.valid) {
      agent.consecutiveMistakeCount++;
      await agent.say('error', overlapValidation.error!);
      pushToolResult(overlapValidation.error!);
      return;
    }

    // Sort operations to avoid line number conflicts:
    // 1. Global replacements (no line numbers) are processed last
    // 2. Line-specific replacements are processed from bottom to top (descending line order)
    const sortedOperations = [...operations].sort((a, b) => {
      const aHasLines = a.start_line !== undefined || a.end_line !== undefined;
      const bHasLines = b.start_line !== undefined || b.end_line !== undefined;

      // If one has line numbers and the other doesn't, prioritize the one with line numbers
      if (aHasLines && !bHasLines) return -1;
      if (!aHasLines && bHasLines) return 1;

      // If both have line numbers, sort by start_line in descending order
      if (aHasLines && bHasLines) {
        const aStartLine = a.start_line ?? 1;
        const bStartLine = b.start_line ?? 1;
        return bStartLine - aStartLine; // Descending order
      }

      // If neither has line numbers, maintain original order
      return 0;
    });

    // Apply all replacement operations from bottom to top
    let newContent = fileContent;
    for (const operation of sortedOperations) {
      newContent = performSingleReplacement(newContent, operation);
    }
    agent.removeLastPartialMessageIfExistsWithType('ask', 'tool');
    await agent.say('tool', JSON.stringify({ ...sharedMessageProps, content: newContent }), block.partial);
    await delay(1000); // wait for diff view to update
    let generationCall = agent.trace?.generation({
      name: 'tool_call',
      input: {
        operations: operations.map((op) => ({
          search: op.search,
          replace: op.replace,
          start_line: op.start_line,
          end_line: op.end_line
        })),
        path: validRelPath,
        newContent
      },
      metadata: {
        name: block.name
      }
    });

    const { data } = await agent.messenger.request('assistant/agent/writeToFile', {
      path: validRelPath,
      content: newContent,
      newFile: false
    });
    reportToolAction(Date.now() - startToolTime, {
      contentLength: newContent.length,
      noModified: !!data?.noModified,
      type: data?.type
    });

    if (data?.noModified) {
      pushToolResult(`No changes needed for '${relPath}'`);
      return;
    }
    if (data?.type === 'success') {
      const resultMessage = [
        `The updated content has been successfully saved to ${validRelPath.toPosix()}. Here is the full, updated content of the file:\n\n`,
        `<final_file_content path="${validRelPath.toPosix()}">\n${newContent}\n</final_file_content>\n\n`,
        `Please note:\n`,
        `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
        `2. Proceed with the task using the updated file content as the new baseline.\n`,
        `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
      ].join('');
      pushToolResult(resultMessage);
    } else {
      pushToolResult(data?.content || '');
    }
    generationCall?.end({
      output: { type: data?.type, content: data?.content }
    });
    agent.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - startToolTime,
      extra4: data?.type === 'success' ? 'success' : 'error',
      extra6: block.name
    });

    await agent.saveCheckpoint();
  } catch (error: any) {
    await handleError('search and replace', error, 'search_and_replace');
  }
}

/**
 * Escapes special regex characters in a string
 * @param input String to escape regex characters in
 * @returns Escaped string safe for regex pattern matching
 */
function escapeRegExp(input: string): string {
  return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
