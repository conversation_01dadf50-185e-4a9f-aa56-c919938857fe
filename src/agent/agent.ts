import { promises as fsPromises } from 'fs';
import { Api } from '@/http';
import { formatResponse } from './prompt/responses';
import {
  WebviewMessage,
  Say,
  Ask,
  ToolResponse,
  MessageParam,
  LocalMessage,
  SayTool,
  EditFileRequest,
  EditFileResponse,
  ExecuteCommandResponse,
  MessageParamVersion0,
  MessageParamVersion1,
  TextBlockParamVersion1,
  ChatRequest
} from './types/type.d';
import cloneDeep from 'clone-deep';
import delay from 'delay';
import pWaitFor from 'p-wait-for';
import { serializeError } from 'serialize-error';
import { AgentLogger, Logger } from '@/util/log';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import { AssistantMessageContent, ToolParamName, ToolUseName } from './types/message';
import { parseAssistantMessage } from './tools/parse-assistant-message';
import { EventSourceMessage } from '@fortaine/fetch-event-source';
import { getReadablePath } from '@/util/path';
import path from 'path';
import { extractTextFromFile } from './tools/extract-text';
import { listFiles } from './tools/list-files';
import { SYSTEM_PROMPT } from './prompt/system';
import { regexSearchFiles } from './tools/ripgrep';
import { callMcpTool, getAvailableMcpServers } from './tools/mcp-tool';
import { ASSISTANT_NAMESPACE, generateCollectionName } from '@/util/const';
import { SearchManager } from '@/indexing/SearchManager';
import { GlobalConfig } from '@/util/global';
import { v4 } from 'uuid';
import { throttle } from 'lodash-es';
import { ContextManager } from './context/ContextManager';
import { TokenCalculator } from './context/TokenCalculator';
import { isAllMessagesVersion1 } from './utils/message';
import CheckpointTracker from './tools/checkpoints/CheckpointTracker';
import pTimeout from 'p-timeout';
import { getTrace } from '@/util/langfuse';
import { LangfuseGenerationClient, LangfuseTraceClient } from 'langfuse';
import { searchAndReplaceTool } from './tools/searchAndReplaceTool';
import { writeToFileTool } from './tools/writeToFileTool';

type UserContent = TextBlockParamVersion1[];
type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export class AgentManager {
  private httpClient = new Api();
  private abortController?: AbortController;
  private readonly agentTraceLogger: InstanceType<typeof AgentLogger>;
  logger = new Logger('assistant-agent');
  readonly sessionId: string;
  readonly chatId: string;
  apiConversationHistory: (MessageParam | MessageParamVersion0)[] = [];
  localMessages: LocalMessage[] = [];
  private askResponse?: AskResponse;
  private askResponseText?: string;
  private lastMessageTs?: number;
  consecutiveMistakeCount: number = 0;
  private isTooLongForApiReq: boolean = false;
  private tooLongTip = '';
  private abort: boolean = false;
  didFinishAbortingStream = false;
  abandoned = false;
  private checkpointTracker?: CheckpointTracker;
  checkpointTrackerErrorMessage?: string;
  private lastCheckpointHash: string = '';
  conversationHistoryDeletedRange?: [number, number];

  // streaming
  isWaitingForFirstChunk = false;
  isStreaming = false;
  private systemPrompt = '';
  private currentStreamingContentIndex = 0;
  private assistantMessageContent: AssistantMessageContent[] = [];
  private presentAssistantMessageLocked = false;
  private presentAssistantMessageHasPendingUpdates = false;
  private userMessageContent: TextBlockParamVersion1[] = [];
  private userMessageContentReady = false;
  private didRejectTool = false;
  private didAlreadyUseTool = false;
  private didCompleteReadingStream = false;
  // private didAutomaticallyRetryFailedApiRequest = false;
  // 追踪任务是否完成
  private didCompleteTask = false;
  private startTaskTime: number = 0;
  private startLlmTime: number = 0;
  private modelConfig: {
    model: string;
    maxTokens: number;
  } = {
    model: 'Kwaipilot Pro',
    maxTokens: 30000
  };

  private contextManager?: ContextManager;
  trace: LangfuseTraceClient | null = null;
  private traceGeneration: LangfuseGenerationClient | null | undefined = null;

  private constructor(
    readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    readonly cwd: string,
    readonly options: {
      model: string;
      maxTokens: number;
      isUseNewTool: boolean;
    },
    private readonly sessionInfo?: WebviewMessage<'newTask'>,
    private readonly restoreInfo?: WebviewMessage<'restore'>
  ) {
    this.startTaskTime = Date.now();
    // 模型
    // this.CURRENT_MODEL = this.sessionInfo?.reqData.model || this.CURRENT_MODEL;
    // 会话ID
    this.sessionId = this.sessionInfo?.reqData.sessionId || this.restoreInfo?.params.sessionId || '';
    // 对话ID
    this.chatId = this.sessionInfo?.reqData.chatId || this.restoreInfo?.params.chatId || '';
    this.agentTraceLogger = new AgentLogger(`s-${this.sessionId}`);
    const enableRepoIndex = true;
    const servers = getAvailableMcpServers(this.agentTraceLogger);
    this.modelConfig.model = options.model;
    this.modelConfig.maxTokens = options.maxTokens;

    // 目前只有 VSCode 使用新版编辑工具
    const useNewEditTool = this.sessionInfo?.reqData.deviceInfo?.ide?.includes('vscode') && !!options.isUseNewTool;
    this.systemPrompt = SYSTEM_PROMPT(
      this.modelConfig.model,
      this.cwd,
      servers,
      enableRepoIndex,
      this.sessionInfo?.rules,
      '',
      useNewEditTool
    );

    this.initializeContextManager();

    this.logger.reportUserAction({
      key: 'agent_task',
      type: 'agent_start_task',
      content: JSON.stringify({
        sessionId: this.sessionId,
        chatId: this.chatId,
        ts: this.startTaskTime,
        enableRepoIndex,
        task: this.sessionInfo?.task,
        mcpServers: servers
      })
    });
    this.logger.info('systemPrompt:', this.systemPrompt);
  }

  // 异步 constructor，保证前期配置一定加载完成
  static async init(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    sessionInfo?: WebviewMessage<'newTask'>,
    restoreInfo?: WebviewMessage<'restore'>
  ) {
    let model = 'Kwaipilot Pro';
    let maxTokens = 30000;
    let isUseNewTool = false;

    try {
      const username = GlobalConfig.getConfig().getUsername();
      // 并行执行两个配置请求
      const [modelConfig, isUseNewToolValue] = await Promise.all([
        fetchModelConfig({
          modelId: sessionInfo?.reqData.model,
          username,
          preference: GlobalConfig.getConfig().getIdeSetting()?.agentPreference
        }),
        fetchGrayConfig({ grayKey: 'newEditFileTool', username })
      ]);

      model = modelConfig.model;
      maxTokens = modelConfig.maxTokens;
      isUseNewTool = isUseNewToolValue;
    } catch (e) {}

    return new AgentManager(messenger, cwd, { model, maxTokens, isUseNewTool }, sessionInfo, restoreInfo);
  }

  private initializeContextManager() {
    // 使用当前模型配置初始化TokenCalculator
    const tokenCalculator = new TokenCalculator(this.modelConfig.model);
    this.contextManager = new ContextManager(this.modelConfig.maxTokens, this.systemPrompt, tokenCalculator);
  }

  async startTask(): Promise<void> {
    // 初始化状态
    this.didCompleteTask = false;
    this.localMessages = this.sessionInfo?.localMessages || [];
    this.apiConversationHistory = (this.sessionInfo?.reqData.messages || []).map((m) => ({
      version: 0,
      ...m
    }));
    const task = this.sessionInfo?.task;
    // 添加@相关信息
    const taskForLlm = this.sessionInfo?.taskForLlm || task || '';
    // 发送第一条任务消息
    await this.say('text', taskForLlm, undefined, 'user');

    // 开始任务循环
    this.handleTaskLoop(
      [
        {
          type: 'text',
          text: `<task>\n${taskForLlm}\n</task>`,
          category: 'user-input'
        }
      ],
      true
    );
  }
  async handleTaskLoop(userContent: UserContent, isNewTask: boolean): Promise<void> {
    let nextUserContent = userContent;
    let includeFileDetails = true;

    this.trace = getTrace(this.sessionId, this.sessionInfo?.reqData.username, {
      sessionId: this.sessionId,
      metadata: {
        ...this.sessionInfo?.reqData,
        systemPrompt: this.systemPrompt
        // model: MODELS.kwaipilotPro32k
      },
      input: userContent
    });

    // 初始化检查点
    await this.initCheckpointTracker();
    await this.saveCheckpoint();

    while (!this.abort) {
      const didEndLoop = await this.recursivelyMakeClineRequests(nextUserContent, includeFileDetails, isNewTask);
      includeFileDetails = false;
      if (didEndLoop) {
        break;
      }
    }
    // dispose
    this.trace = null;
    this.traceGeneration = null;
  }

  async recursivelyMakeClineRequests(
    userContent: UserContent,
    includeFileDetails: boolean = false,
    isNewTask: boolean = false
  ): Promise<boolean> {
    this.startLlmTime = Date.now();
    if (this.abort) throw new Error('Kwaipilot instance aborted');
    // 检查任务是否已完成，如果任务已完成，直接返回true结束循环
    if (this.didCompleteTask) return true;

    await this.handleApiRequestLimits(userContent);
    await this.say('api_req_started', JSON.stringify(userContent));

    // 创建新的 abortController
    this.agentTraceLogger.info(`废弃旧的abortController，创建新的abortController`);
    this.abortController?.abort();
    this.abortController = new AbortController();
    const innerAbortController = this.abortController;

    // 添加环境信息
    const environmentDetails = await this.getEnvironmentDetails(includeFileDetails);
    userContent.unshift({ type: 'text', text: environmentDetails, category: 'environment-details' });

    await this.addToApiConversationHistory(
      {
        role: 'user',
        content: userContent,
        chatId: this.chatId
      },
      isNewTask
    );

    this.resetStreamingState();
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-pre-llm',
      millis: Date.now() - this.startLlmTime
    });
    try {
      const messages =
        isAllMessagesVersion1(this.apiConversationHistory) && this.contextManager
          ? (await this.contextManager.optimizeMessagesContext(this.apiConversationHistory, this.trace)).messages
          : this.apiConversationHistory;
      const reqParams: ChatRequest = {
        ...(this.sessionInfo?.reqData || {}),
        sessionId: this.sessionId,
        messages: messages.map((m) => ({
          content: m.content,
          role: m.role,
          chatId: m.chatId
        })),
        mode: GlobalConfig.getConfig().getIdeSetting()?.agentPreference,
        systemPrompt: this.systemPrompt
        // model: 'CLAUDE_3',
      };
      let assistantMessage = '';
      this.isStreaming = true;
      this.agentTraceLogger.info(`历史对话记录messages: ${JSON.stringify(messages)}`);
      const reqUUID = v4();
      // 创建一个 Promise 来追踪所有消息处理
      const messageProcessingPromise = new Promise<void>((resolve, reject) => {
        let isComplete = false;
        let hasError = false;
        this.agentTraceLogger.info(reqUUID, '开始请求');
        this.agentTraceLogger.debug(reqUUID, `请求体 ${JSON.stringify(reqParams)}`);
        this.traceGeneration = this.trace?.generation({
          name: 'chat-completion',
          input: messages
        });
        let output = '';
        const startApiTime = Date.now();
        this.logger.reportUserAction({
          key: 'agent_task',
          type: 'agent_llm_api_start',
          content: JSON.stringify({
            sessionId: this.sessionId,
            chatId: this.chatId,
            requestId: reqUUID,
            ts: startApiTime
          })
        });
        let returnFirstToken = false;
        this.httpClient
          .fetchEventSource('/eapi/kwaipilot/plugin/composer/chat/completions', {
            method: 'POST',
            body: JSON.stringify(reqParams),
            headers: {
              'Content-type': 'application/json;charset=UTF-8'
            },
            signal: innerAbortController?.signal,
            onclose: () => {
              this.logger.perf({
                namespace: ASSISTANT_NAMESPACE,
                subtag: 'kwaipilot-ide-agent-chat-api',
                millis: Date.now() - startApiTime,
                extra4: `close`,
                extra6: this.modelConfig.model
              });
              this.logger.reportUserAction({
                key: 'agent_task',
                type: 'agent_llm_api_close',
                content: JSON.stringify({
                  sessionId: this.sessionId,
                  chatId: this.chatId,
                  requestId: reqUUID,
                  ts: Date.now(),
                  duration: Date.now() - startApiTime,
                  modelName: this.modelConfig.model
                })
              });
              this.agentTraceLogger.info(reqUUID, '模型请求接口关闭');
              if (innerAbortController?.signal.aborted) {
                this.agentTraceLogger.info(reqUUID, '模型请求接口关闭，fetchEventSource aborted');
                this.traceGeneration?.end({
                  output: { result: '模型请求接口关闭，fetchEventSource aborted' }
                });
                return;
              }
              if (!isComplete) {
                resolve();
              }
            },
            onmessage: async (event: EventSourceMessage) => {
              if (!returnFirstToken) {
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-first-token',
                  millis: Date.now() - startApiTime,
                  extra4: `success`,
                  extra6: this.modelConfig.model
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'agent_llm_api_frist_token',
                  content: JSON.stringify({
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    requestId: reqUUID,
                    ts: Date.now(),
                    duration: Date.now() - startApiTime,
                    modelName: this.modelConfig.model
                  })
                });
                returnFirstToken = true;
              }
              if (innerAbortController?.signal.aborted) {
                this.agentTraceLogger.info(reqUUID, 'fetchEventSource aborted');
                return;
              }
              const data = JSON.parse(event.data);
              const traceId = data.traceId;
              if (data.code === 413) {
                this.isTooLongForApiReq = true;
                this.tooLongTip = data.tip;
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-llm',
                  millis: Date.now() - this.startLlmTime,
                  extra4: `error`,
                  extra6: `api_req_isToolong`
                });
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-llm-total',
                  millis: Date.now() - this.startLlmTime,
                  extra4: this.modelConfig.model
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'agent_llm_api_error',
                  content: JSON.stringify({
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    requestId: reqUUID,
                    ts: Date.now(),
                    duration: Date.now() - this.startLlmTime,
                    error: 'api_req_isToolong',
                    modelName: this.modelConfig.model
                  })
                });
                return;
              }
              try {
                // this.logger.info(
                //   `"fetcheEventSource replyyyyyyyyy:", ${JSON.stringify(data)}`
                // );
                const delta = data.message.content;
                assistantMessage += delta;
                output = assistantMessage;
                this.agentTraceLogger.info(reqUUID, traceId, `delta: ${delta}`);
                this.agentTraceLogger.debug(reqUUID, traceId, `流式返回信息: ${assistantMessage}`);
                const prevLength = this.assistantMessageContent.length;
                this.assistantMessageContent = parseAssistantMessage(assistantMessage);

                if (this.assistantMessageContent.length > prevLength) {
                  this.userMessageContentReady = false;
                }

                this.presentAssistantMessage();

                if (this.didRejectTool) {
                  this.agentTraceLogger.info(reqUUID, 'didRejectTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage += '\n\n[Response interrupted by user feedback]';
                  resolve();
                }

                if (this.didAlreadyUseTool) {
                  this.agentTraceLogger.info(reqUUID, 'didAlreadyUseTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage +=
                    '\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]';
                  resolve();
                }
                return;
              } catch (error: any) {
                this.agentTraceLogger.info(reqUUID, '消息解析出错');
                this.agentTraceLogger.error(reqUUID, `消息解析出错: ${error}`);
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'parse-assistant-message-error',
                  millis: 1,
                  extra4: `消息解析出错：${JSON.stringify(error)}`,
                  extra6: new Error().stack || ''
                });
                this.say('error', `消息解析出错: ${error}`);
                reject(error);
              }
            },
            onerror: (e: any) => {
              this.logger.perf({
                namespace: ASSISTANT_NAMESPACE,
                subtag: 'kwaipilot-ide-agent-chat-api',
                millis: Date.now() - startApiTime,
                extra4: `error`,
                extra6: this.modelConfig.model
              });
              this.logger.reportUserAction({
                key: 'agent_task',
                type: 'agent_llm_api_error',
                content: JSON.stringify({
                  sessionId: this.sessionId,
                  chatId: this.chatId,
                  requestId: reqUUID,
                  ts: Date.now(),
                  duration: Date.now() - startApiTime,
                  error: JSON.stringify(e),
                  modelName: this.modelConfig.model
                })
              });
              if (!hasError && !isComplete) {
                // 处理错误...
                hasError = true;
                isComplete = true;
                this.agentTraceLogger.info(reqUUID, `模型请求接口出错: ${e}`);
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: '/eapi/kwaipilot/plugin/composer/chat/completions',
                  millis: 1,
                  extra4: `模型请求接口出错：${JSON.stringify(e)}`,
                  extra6: new Error().stack || ''
                });
                reject(e);
                this.traceGeneration?.end({
                  output: {
                    msg: `模型请求接口出错：${JSON.stringify(e)}`,
                    stack: new Error().stack || ''
                  }
                });
              }
            }
          })
          .then(() => {
            this.trace?.update({
              output
            });
            this.traceGeneration?.end({ output });
          });
      });
      try {
        // 使用Promise包装事件流处理
        await messageProcessingPromise;
        this.agentTraceLogger.info(reqUUID, '消息处理已完成:', assistantMessage);
      } catch (error) {
        // 捕获并记录错误，但仍然继续执行
        this.agentTraceLogger.info(reqUUID, `消息处理过程中出错: ${error}`);
        this.logger.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'message-processing-error',
          millis: 1,
          extra4: JSON.stringify(error),
          extra6: new Error().stack || ''
        });
      } finally {
        // 后续处理
        this.isStreaming = false;
        this.didCompleteReadingStream = true;

        const partialBlocks = this.assistantMessageContent.filter((block) => block.partial);
        partialBlocks.forEach((block) => {
          block.partial = false;
        });
        if (partialBlocks.length > 0) {
          this.presentAssistantMessage();
        }
        let didEndLoop = false;
        if (assistantMessage.length > 0) {
          await this.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: assistantMessage,
                category: 'assistant'
              }
            ],
            chatId: this.chatId
          });
          await pWaitFor(() => this.userMessageContentReady);
          const didToolUse = this.assistantMessageContent.some((block) => block.type === 'tool_use');

          if (!didToolUse) {
            await this.handleCompletedTask();
            return true;
          }
          this.logger.perf({
            namespace: ASSISTANT_NAMESPACE,
            subtag: 'kwaipilot-ide-agent-chat-llm',
            millis: Date.now() - this.startLlmTime,
            extra4: `success`
          });
          this.logger.perf({
            namespace: ASSISTANT_NAMESPACE,
            subtag: 'kwaipilot-ide-agent-llm-total',
            millis: Date.now() - this.startLlmTime,
            extra4: this.modelConfig.model
          });
          this.logger.reportUserAction({
            key: 'agent_task',
            type: 'agent_llm_end',
            content: JSON.stringify({
              sessionId: this.sessionId,
              chatId: this.chatId,
              requestId: reqUUID,
              ts: Date.now(),
              duration: Date.now() - this.startLlmTime,
              modelName: this.modelConfig.model
            })
          });

          const recDidEndLoop = await this.recursivelyMakeClineRequests(this.userMessageContent);
          didEndLoop = recDidEndLoop || this.didCompleteTask;
        } else {
          if (!this.isTooLongForApiReq) {
            // await this.say('api_req_failed', 'API请求出错');
            this.logger.perf({
              namespace: ASSISTANT_NAMESPACE,
              subtag: 'kwaipilot-ide-agent-chat-llm',
              millis: Date.now() - this.startLlmTime,
              extra4: `error`,
              extra6: `api_req_failed`
            });
            this.logger.perf({
              namespace: ASSISTANT_NAMESPACE,
              subtag: 'kwaipilot-ide-agent-llm-total',
              millis: Date.now() - this.startLlmTime,
              extra4: this.modelConfig.model
            });
            this.logger.reportUserAction({
              key: 'agent_task',
              type: 'agent_llm_end_error',
              content: JSON.stringify({
                sessionId: this.sessionId,
                chatId: this.chatId,
                requestId: reqUUID,
                ts: Date.now(),
                duration: Date.now() - this.startLlmTime,
                error: 'api_req_failed',
                modelName: this.modelConfig.model
              })
            });
          }
          await this.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: 'Failure: I did not provide a response.',
                category: 'assistant'
              }
            ],
            chatId: this.chatId
          });
          this.consecutiveMistakeCount++;
        }

        return didEndLoop;
      }
    } catch (error: any) {
      this.agentTraceLogger.error(`模型请求出错: ${error}`);
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-chat-llm',
        millis: 1,
        extra4: `error`,
        extra6: `${JSON.stringify(error)}`
      });
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-llm-total',
        millis: Date.now() - this.startLlmTime,
        extra4: this.modelConfig.model
      });
      this.logger.reportUserAction({
        key: 'agent_task',
        type: 'agent_llm_process_error',
        content: JSON.stringify({
          sessionId: this.sessionId,
          chatId: this.chatId,
          ts: Date.now(),
          duration: Date.now() - this.startLlmTime,
          error: 'api_req_failed',
          modelName: this.modelConfig.model
        })
      });
      return true;
    }
  }

  private resetStreamingState() {
    // 当前正在处理流的索引位置
    this.currentStreamingContentIndex = 0;
    // 存储 AI 助手的消息内容的数组
    this.assistantMessageContent = [];
    // 标记是否已完成读取整个流
    this.didCompleteReadingStream = false;
    // 存储用户消息内容的数组，包含用户输入和反馈的消息
    this.userMessageContent = [];
    // 标记用户消息内容是否准备就绪
    this.userMessageContentReady = false;
    // 标记是否拒绝工具
    this.didRejectTool = false;
    // 标记是否已经使用工具
    this.didAlreadyUseTool = false;
    // 标记助手消息展示是否被锁定，用于防止消息展示的并发问题
    this.presentAssistantMessageLocked = false;
    // 标记是否存在待处理的助手消息更新
    this.presentAssistantMessageHasPendingUpdates = false;
    // 标记是否已经自动重试过失败的 API 请求
    // this.didAutomaticallyRetryFailedApiRequest = false;
  }

  private async handleApiRequestLimits(userContent: UserContent) {
    if (this.isTooLongForApiReq) {
      const { askResponse, text } = await this.ask('mistake_limit_reached', this.tooLongTip);
      if (askResponse === 'messageResponse' && text) {
        userContent.push({ type: 'text', text, category: 'user-input' });
      }
      this.isTooLongForApiReq = false;
      this.tooLongTip = '';
    }
    if (this.consecutiveMistakeCount >= 3) {
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-chat-all',
        millis: Date.now() - this.startTaskTime,
        extra4: `error`,
        extra6: `mistake_limit_reached`
      });
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-agent-total',
        millis: Date.now() - this.startTaskTime,
        extra4: this.modelConfig.model
      });
      this.logger.reportUserAction({
        key: 'agent_task',
        type: 'agent_task_end_error',
        content: JSON.stringify({
          sessionId: this.sessionId,
          chatId: this.chatId,
          ts: Date.now(),
          duration: Date.now() - this.startTaskTime,
          error: 'mistake_limit_reached',
          modelName: this.modelConfig.model
        })
      });
      const { askResponse, text } = await this.ask(
        'mistake_limit_reached',
        `抱歉，服务请求失败，建议稍后再试或联系 @Kwaipilot 智能客服。`
      );
      if (askResponse === 'messageResponse') {
        userContent.push({ type: 'text', text: formatResponse.tooManyMistakes(text), category: 'format-response' });
      }
      this.consecutiveMistakeCount = 0;
    }
  }

  async presentAssistantMessage() {
    if (this.abort) {
      throw new Error('Kwaipilot instance aborted');
    }
    // 如果函数被锁定，标记有待处理的更新，并返回
    if (this.presentAssistantMessageLocked) {
      this.presentAssistantMessageHasPendingUpdates = true;
      return;
    }
    // 执行中，锁定该函数，待处理为false
    this.presentAssistantMessageLocked = true;
    this.presentAssistantMessageHasPendingUpdates = false;

    if (this.currentStreamingContentIndex >= this.assistantMessageContent.length) {
      if (this.didCompleteReadingStream) {
        this.userMessageContentReady = true;
      }
      this.presentAssistantMessageLocked = false;
      return;
    }

    let generationCall: LangfuseGenerationClient | null | undefined = null;
    const block = cloneDeep(this.assistantMessageContent[this.currentStreamingContentIndex]);
    switch (block.type) {
      case 'text': {
        if (this.didRejectTool || this.didAlreadyUseTool) {
          break;
        }
        let content = block.content;
        if (content) {
          content = content.replace(/<thinking>\s?/g, '');
          content = content.replace(/\s?<\/thinking>/g, '');
          const lastOpenBracketIndex = content.lastIndexOf('<');
          if (lastOpenBracketIndex !== -1) {
            const possibleTag = content.slice(lastOpenBracketIndex);
            const hasCloseBracket = possibleTag.includes('>');
            if (!hasCloseBracket) {
              // Extract the potential tag name
              let tagContent: string;
              if (possibleTag.startsWith('</')) {
                tagContent = possibleTag.slice(2).trim();
              } else {
                tagContent = possibleTag.slice(1).trim();
              }
              // Check if tagContent is likely an incomplete tag name (letters and underscores only)
              const isLikelyTagName = /^[a-zA-Z_]+$/.test(tagContent);
              // Preemptively remove < or </ to keep from these artifacts showing up in chat (also handles closing thinking tags)
              const isOpeningOrClosing = possibleTag === '<' || possibleTag === '</';
              // If the tag is incomplete and at the end, remove it from the content
              if (isOpeningOrClosing || isLikelyTagName) {
                content = content.slice(0, lastOpenBracketIndex).trim();
              }
            }
          }
        }

        if (!block.partial) {
          // Some models add code block artifacts (around the tool calls) which show up at the end of text content
          // matches ``` with at least one char after the last backtick, at the end of the string
          const match = content?.trimEnd().match(/```[a-zA-Z0-9_-]+$/);
          if (match) {
            const matchLength = match[0].length;
            content = content.trimEnd().slice(0, -matchLength);
          }
        }

        await this.say('text', content, block.partial);
        break;
      }
      case 'tool_use':
        const toolName = block.name;
        const params = block.params;
        const toolDescription = () => {
          switch (toolName) {
            case 'execute_command':
              return `[${toolName} for '${block.params.command}']`;
            case 'read_file':
              return `[${toolName} for '${block.params.path}' ${
                block.params.should_read_entire_file === 'false' && block.params.end_line_one_indexed_inclusive
                  ? `from line ${block.params.start_line_one_indexed} to line ${block.params.end_line_one_indexed_inclusive}`
                  : ''
              }]`;
            case 'edit_file':
              return `[${toolName} for '${block.params.target_file}']`;
            case 'search_and_replace':
              return `[${block.name} for '${block.params.path}']`;
            case 'write_to_file':
              return `[${block.name} for '${block.params.path}']`;
            case 'codebase_search':
              return `[${toolName} for '${block.params.query}']`;
            case 'grep_search':
              return `[${toolName} for '${block.params.regex}'${
                block.params.file_pattern ? ` in '${block.params.file_pattern}'` : ''
              }]`;
            case 'list_files':
              return `[${toolName} for '${block.params.path}']`;
            case 'ask_followup_question':
              return `[${toolName} for '${block.params.question}']`;
            case 'use_mcp_tool':
              return `[${toolName}] for '${block.params.server_name}' and '${block.params.tool_name}'`;
          }
        };

        if (this.didRejectTool) {
          if (!block.partial) {
            this.userMessageContent.push({
              type: 'text',
              text: `Skipping tool ${toolDescription()} due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params
            });
          } else {
            // partial tool after user rejected a previous tool
            this.userMessageContent.push({
              type: 'text',
              text: `Tool ${toolDescription()} was interrupted and not executed due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params
            });
          }
          break;
        }

        if (this.didAlreadyUseTool) {
          // ignore any content after a tool has already been used
          this.userMessageContent.push({
            type: 'text',
            text: `Tool [${toolName}] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.`,
            category: 'tool-exception',
            toolName,
            params
          });
          break;
        }

        const pushToolResult = (content: ToolResponse) => {
          this.userMessageContent.push({
            type: 'text',
            text: `${toolDescription()} Result:`,
            category: 'tool-title',
            toolName,
            params
          });
          if (typeof content === 'string') {
            this.userMessageContent.push({
              type: 'text',
              text: content || '(tool did not return anything)',
              category: 'tool-response',
              toolName,
              params
            });
          } else {
            const formattedContentList = content.map<TextBlockParamVersion1>((c) => ({
              ...c,
              category: 'tool-response',
              toolName,
              params
            }));
            this.userMessageContent.push(...formattedContentList);
          }
          // once a tool result has been collected, ignore all other tool uses since we should only ever present one tool result per message
          this.didAlreadyUseTool = true;
        };

        // The user can approve, reject, or provide feedback (rejection). However the user may also send a message along with an approval, in which case we add a separate user message with this feedback.
        const pushAdditionalToolFeedback = (feedback?: string) => {
          if (!feedback) {
            return;
          }
          const content = `The user provided the following feedback:\n<feedback>\n${feedback}\n</feedback>`;
          this.userMessageContent.push({
            type: 'text',
            text: content,
            category: 'tool-feedback',
            toolName,
            params
          });
        };

        const askApproval = async (type: Ask, partialMessage?: string) => {
          const { askResponse, text } = await this.ask(type, partialMessage, false);
          if (askResponse !== 'yesButtonClicked') {
            // User pressed reject button or responded with a message, which we treat as a rejection
            pushToolResult(formatResponse.toolDenied());
            if (text) {
              pushAdditionalToolFeedback(text);
              await this.say('user_feedback', text);
            }
            this.didRejectTool = true; // Prevent further tool uses in this message
            return false;
          } else {
            // User hit the approve button, and may have provided feedback
            if (text) {
              pushAdditionalToolFeedback(text);
              await this.say('user_feedback', text);
            }
            return true;
          }
        };

        const handleError = async (action: string, error: Error, toolName: ToolUseName) => {
          const errorString = `Error ${action}: ${JSON.stringify(serializeError(error))}`;
          await this.say(
            'tool_error',
            `Error ${action}:\n${error.message ?? JSON.stringify(serializeError(error), null, 2)}`
          );
          pushToolResult(formatResponse.toolError(errorString));
          this.logger.perf({
            namespace: ASSISTANT_NAMESPACE,
            subtag: 'kwaipilot-ide-agent-chat-tool',
            millis: Date.now() - this.startLlmTime,
            extra4: 'error',
            extra6: toolName
          });
          generationCall?.end({
            output: {
              msg: error.message
            }
          });
        };

        // If block is partial, remove partial closing tag so its not presented to user
        const removeClosingTag = (tag: ToolParamName, text?: string) => {
          if (!block.partial) {
            return text || '';
          }
          if (!text) {
            return '';
          }
          // This regex dynamically constructs a pattern to match the closing tag:
          // - Optionally matches whitespace before the tag
          // - Matches '<' or '</' optionally followed by any subset of characters from the tag name
          const tagRegex = new RegExp(
            `\\s?<\/?${tag
              .split('')
              .map((char) => `(?:${char})?`)
              .join('')}$`,
            'g'
          );
          return text.replace(tagRegex, '');
        };

        const generateToolLog = (toolName: string) => {
          const uuid = v4();
          return {
            start: (content: string) => {
              this.agentTraceLogger.info(`tool}-开始${toolName}-${uuid}`, content);
            },
            end: (details: string) => {
              this.agentTraceLogger.debug(`tool}-结束${toolName}-${uuid}`, details);
              this.agentTraceLogger.info(`tool}-结束${toolName}-${uuid}`);
            }
          };
        };
        const reportToolAction = (duration: number, params: Record<string, any>) => {
          this.logger.reportUserAction({
            key: 'agent_task',
            type: 'tool_use',
            content: JSON.stringify({
              toolName: block.name,
              sessionId: this.sessionId,
              chatId: this.chatId,
              ts: Date.now(),
              duration,
              params
            })
          });
        };
        switch (block.name) {
          case 'search_and_replace':
            await searchAndReplaceTool(this, block, handleError, pushToolResult, removeClosingTag, reportToolAction);
            break;
          case 'write_to_file':
            await writeToFileTool(
              this,
              block,
              askApproval,
              handleError,
              pushToolResult,
              removeClosingTag,
              reportToolAction
            );
            break;
          case 'edit_file': {
            const target_file: string | undefined = block.params.target_file;
            let code_edit: string | undefined = block.params.code_edit;
            let instructions: string | undefined = block.params.instructions;
            let language: string | undefined = block.params.language;

            try {
              const sharedMessageProps: SayTool = {
                tool: 'editFile',
                content: removeClosingTag('code_edit', code_edit),
                instructions: removeClosingTag('instructions', instructions),
                language: removeClosingTag('language', language),
                path: target_file
              };

              if (block.partial) {
                const partialMessage = JSON.stringify(sharedMessageProps);
                if (!block.params.requires_approval) {
                  this.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
                  await this.say('tool', partialMessage, block.partial, undefined);
                } else {
                  this.removeLastPartialMessageIfExistsWithType('say', 'tool');
                  await this.ask('tool', partialMessage, block.partial, undefined).catch(() => {});
                }
                break;
              } else {
                if (!target_file) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError(block.name, 'target_file'));

                  break;
                }
                if (!code_edit) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError(block.name, 'code_edit'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'edit_file'
                });
                const completeMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: code_edit
                } satisfies SayTool);

                if (!block.params.requires_approval) {
                  this.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
                  await this.say('tool', completeMessage, block.partial, undefined);
                  await delay(3_500);
                } else {
                  this.removeLastPartialMessageIfExistsWithType('say', 'tool');
                  await this.ask('tool', completeMessage, block.partial, undefined);
                  let didApprove = true;
                  const { askResponse, text } = await this.ask('tool', completeMessage, false);
                  if (askResponse !== 'yesButtonClicked') {
                    // User either sent a message or pressed reject button
                    pushToolResult(`The user denied this operation.`);
                    if (text) {
                      pushAdditionalToolFeedback(text);
                      await this.say('user_feedback', text);
                    }
                    this.didRejectTool = true;
                    didApprove = false;
                    break;
                  } else {
                    // User hit the approve button, and may have provided feedback
                    if (text) {
                      pushAdditionalToolFeedback(text);
                      await this.say('user_feedback', text);
                    }
                  }
                }
                
                let fileContent = '';
                try {
                  const absolutePath = path.resolve(this.cwd, target_file);
                  fileContent = await fsPromises.readFile(absolutePath, 'utf-8');
                } catch (error) {
                  // 数据只用于日志上报，不处理
                }
                const toolLog = generateToolLog('edit_file');
                toolLog.start(target_file);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    content: removeClosingTag('code_edit', code_edit),
                    instructions: removeClosingTag('instructions', instructions),
                    language: removeClosingTag('language', language),
                    path: target_file
                  },
                  metadata: {
                    name: block.name
                  }
                });
                // 调用edit_file工具
                const startToolTime = Date.now();
                const { type, content, newFile } = await this.onEditFile({
                  content: removeClosingTag('code_edit', code_edit),
                  instructions: removeClosingTag('instructions', instructions),
                  language: removeClosingTag('language', language) as any,
                  path: target_file,
                  lastCheckpointHash: this.lastCheckpointHash
                });
                toolLog.end(`${type} ${content}`);
                generationCall?.end({
                  output: { type, content, newFile }
                });
                let result = '';
                if (typeof newFile === 'boolean') {
                  result = JSON.stringify({ type, content, newFile });
                } else {
                  result = JSON.stringify({ type, content });
                }
                pushToolResult(content);
                this.say('edit_file_result', result);

                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: type === 'success' ? 'success' : 'error',
                  extra6: block.name
                });
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-editFile',
                  millis: Date.now() - startToolTime,
                  extra4: type === 'success' ? 'success' : 'error',
                  extra6: String(fileContent.split('\n').length)
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      resType: type,
                      lines: fileContent.split('\n').length,
                      newFile
                    }
                  })
                });
                await this.saveCheckpoint();
                break;
              }
            } catch (error: any) {
              await handleError('editing file', error, 'edit_file');
              break;
            }
          }
          case 'read_file': {
            const relPath: string | undefined = block.params.path;
            const sharedMessageProps: SayTool = {
              tool: 'readFile',
              path: getReadablePath(this.cwd, removeClosingTag('path', relPath))
            };
            try {
              if (block.partial) {
                const partialMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: undefined
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
                await this.say('tool', partialMessage, block.partial);
                break;
              } else {
                if (!relPath) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('read_file', 'path'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'read_file'
                });
                const absolutePath = path.resolve(this.cwd, relPath);
                const startLine: number | undefined = block.params.start_line_one_indexed
                  ? parseInt(block.params.start_line_one_indexed)
                  : undefined;
                const endLine: number | undefined = block.params.end_line_one_indexed_inclusive
                  ? parseInt(block.params.end_line_one_indexed_inclusive)
                  : undefined;
                const shouldReadEntireFile: boolean | undefined = block.params.should_read_entire_file === 'true';
                const completeMessage = JSON.stringify({
                  ...sharedMessageProps,
                  startLine,
                  endLine,
                  shouldReadEntireFile,
                  content: absolutePath
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', completeMessage, false);
                const toolLog = generateToolLog('read_file');
                toolLog.start(absolutePath);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    path: absolutePath
                  },
                  metadata: {
                    name: block.name
                  }
                });
                // now execute the tool like normal
                const startToolTime = Date.now();
                const { content, totalLineNum, readedLineNum } = await extractTextFromFile(
                  absolutePath,
                  shouldReadEntireFile
                    ? undefined
                    : {
                        startLine,
                        endLine
                      }
                );
                toolLog.end(content);
                generationCall?.end({
                  output: { content, totalLineNum }
                });
                pushToolResult(content);
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      shouldReadEntireFile,
                      totalLineNum,
                      readedLineNum,
                      path: absolutePath,
                      startLine,
                      endLine
                    }
                  })
                });
                break;
              }
            } catch (error: any) {
              await handleError('reading file', error, 'read_file');

              break;
            }
          }
          case 'list_files': {
            const relDirPath: string | undefined = block.params.path;
            const recursiveRaw: string | undefined = block.params.recursive;
            const recursive = recursiveRaw?.toLowerCase() === 'true';
            const sharedMessageProps: SayTool = {
              tool: !recursive ? 'listFilesTopLevel' : 'listFilesRecursive',
              path: getReadablePath(this.cwd, removeClosingTag('path', relDirPath))
            };
            try {
              if (block.partial) {
                const partialMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: ''
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', partialMessage, block.partial);

                break;
              } else {
                if (!relDirPath) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('list_files', 'path'));

                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'list_files'
                });
                const absolutePath = path.resolve(this.cwd, relDirPath);

                const toolLog = generateToolLog('list_files');
                toolLog.start(`${absolutePath} ${recursive}`);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    path: absolutePath,
                    recursive
                  },
                  metadata: {
                    name: block.name
                  }
                });
                const startToolTime = Date.now();
                const [files, didHitLimit] = await listFiles(absolutePath, recursive, 200);
                toolLog.end(JSON.stringify(files));
                generationCall?.end({
                  output: { files, didHitLimit }
                });

                const result = formatResponse.formatFilesList(absolutePath, files, didHitLimit);
                const completeMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: result
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', completeMessage, false);
                pushToolResult(result);
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      path: absolutePath,
                      recursive,
                      didHitLimit
                    }
                  })
                });
                break;
              }
            } catch (error: any) {
              await handleError('listing files', error, 'list_files');

              break;
            }
          }
          case 'codebase_search': {
            const query: string | undefined = block.params.query;
            const target_directories: string | undefined = block.params.target_directories;
            const sharedMessageProps: SayTool = {
              tool: 'codebaseSearch',
              query: query,
              target_directories: removeClosingTag('target_directories', target_directories)
            };
            try {
              if (block.partial) {
                const partialMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: ''
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', partialMessage, block.partial);
                break;
              } else {
                if (!query) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('codebase_search', 'query'));

                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'codebase_search'
                });
                const collectionName = generateCollectionName(this.cwd);
                const searchManager = new SearchManager(collectionName);
                const toolLog = generateToolLog('codebase_search');
                toolLog.start(`${query} ${target_directories}`);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    query,
                    target_directories
                  },
                  metadata: {
                    name: block.name
                  }
                });
                const startToolTime = Date.now();

                const rerankResult = await searchManager.search({
                  query,
                  topK: 20, // limit to 20 results
                  targetDirectory: target_directories ? [target_directories] : [],
                  chatHistory: this.apiConversationHistory,
                  enable_rewrite: false,
                  gitRepo: this.sessionInfo?.reqData.projectInfo?.gitUrl,
                  commit: '',
                  username: this.sessionInfo?.reqData.username || GlobalConfig.getConfig().getUsername(),
                  enableCloudSearch: true
                });

                toolLog.end(JSON.stringify(rerankResult));
                generationCall?.end({
                  output: { rerankResult }
                });
                const completeMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: JSON.stringify(rerankResult)
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', completeMessage, false);
                pushToolResult(JSON.stringify(rerankResult));
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      query,
                      target_directories
                    }
                  })
                });
                break;
              }
            } catch (error: any) {
              await handleError('codebase searching files', error, 'codebase_search');
              break;
            }
          }
          case 'grep_search': {
            const relDirPath: string | undefined = block.params.path;
            const regex: string | undefined = block.params.regex;
            const filePattern: string | undefined = block.params.file_pattern;
            const sharedMessageProps: SayTool = {
              tool: 'grepSearch',
              path: getReadablePath(this.cwd, removeClosingTag('path', relDirPath)),
              regex: removeClosingTag('regex', regex),
              filePattern: removeClosingTag('file_pattern', filePattern)
            };
            try {
              if (block.partial) {
                const partialMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: ''
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', partialMessage, block.partial);
                break;
              } else {
                if (!relDirPath) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('grep_search', 'path'));
                  break;
                }
                if (!regex) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('grep_search', 'regex'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'grep_search'
                });
                const absolutePath = path.resolve(this.cwd, relDirPath);
                const toolLog = generateToolLog('grep_search');
                toolLog.start(`${absolutePath} ${regex} ${filePattern}`);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    path: absolutePath,
                    regex,
                    filePattern
                  },
                  metadata: {
                    name: block.name
                  }
                });
                const startToolTime = Date.now();
                const results = await regexSearchFiles(this.cwd, absolutePath, regex, filePattern);
                toolLog.end(results);
                generationCall?.end({
                  output: { results }
                });
                const completeMessage = JSON.stringify({
                  ...sharedMessageProps,
                  content: results
                } satisfies SayTool);
                this.removeLastPartialMessageIfExistsWithType('ask', 'tool');
                await this.say('tool', completeMessage, false);
                pushToolResult(results);
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      path: absolutePath,
                      regex,
                      filePattern
                    }
                  })
                });
                break;
              }
            } catch (error: any) {
              await handleError('grep searching files', error, 'grep_search');
              break;
            }
          }
          case 'execute_command': {
            const command: string | undefined = block.params.command;
            const is_background: string | undefined = block.params.is_background;
            const is_background_boolean = is_background?.toLowerCase() === 'true';
            const messsage = JSON.stringify({
              tool: 'executeCommand',
              command: removeClosingTag('command', command),
              is_background: is_background_boolean
            });
            try {
              if (block.partial) {
                // if (!requiresApproval) {
                //   //
                // } else {
                //   await this.ask("command", removeClosingTag("command", command), block.partial).catch(() => { })
                // }
                await this.ask('command', messsage, block.partial).catch(() => {});
                break;
              } else {
                if (!command) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('execute_command', 'command'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'execute_command'
                });
                const startAskTime = Date.now();
                const didApprove = await askApproval('command', messsage);
                this.logger.reportUserAction({
                  key: 'agent_tools_request_ope',
                  type: 'execute_command',
                  subType: didApprove ? 'tool_run' : 'tool_cancel'
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use_ask',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startAskTime,
                    didApprove
                  })
                });
                if (!didApprove) {
                  break;
                }
                await this.say('command_output', '', true);
                const toolLog = generateToolLog('execute_command');
                toolLog.start(`${command} ${is_background_boolean}`);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    command,
                    is_background_boolean
                  },
                  metadata: {
                    name: block.name
                  }
                });
                // 执行命令
                const startToolTime = Date.now();
                const { userRejected, result, cutRes } = await this.executeCommandTool(command, is_background_boolean);

                toolLog.end(result);
                generationCall?.end({
                  output: { result }
                });
                if (userRejected) {
                  this.didRejectTool = true;
                }
                // VS Code 的文件系统监听器有限制：
                // - 只能监听到用户手动的文件操作
                // - 无法自动检测程序创建/删除的文件
                // - 需要手动触发更新来保持同步
                pushToolResult(cutRes);
                await this.say('command_output', result, false);

                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      command,
                      is_background
                    }
                  })
                });
                await this.saveCheckpoint();
                break;
              }
            } catch (error: any) {
              await handleError('executing command', error, 'execute_command');
              break;
            }
          }
          case 'use_mcp_tool': {
            const mcpServerName: string | undefined = block.params.server_name;
            const mcpToolName: string | undefined = block.params.tool_name;
            const mcpToolParams: string | undefined = block.params.arguments;
            const params = {
              tool: 'useMcpTool',
              serverName: mcpServerName,
              toolName: mcpToolName,
              arguments: mcpToolParams
            };
            const messsage = JSON.stringify(params);
            try {
              if (block.partial) {
                await this.ask('use_mcp_tool', messsage, block.partial).catch(() => {});
                break;
              } else {
                if (!mcpServerName) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('use_mcp_tool', 'serverName'));
                  break;
                }
                if (!mcpToolName) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('use_mcp_tool', 'toolName'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'use_mcp_tool',
                  content: JSON.stringify({
                    mcp_server_name: mcpServerName,
                    mcp_tool_name: mcpToolName
                  })
                });
                const startAskTime = Date.now();
                const didApprove = await askApproval('use_mcp_tool', messsage);
                this.logger.reportUserAction({
                  key: 'agent_tools_request_ope',
                  type: 'use_mcp_tool',
                  subType: didApprove ? 'tool_run' : 'tool_cancel',
                  content: JSON.stringify({
                    mcp_server_name: mcpServerName,
                    mcp_tool_name: mcpToolName
                  })
                });

                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use_ask',
                  content: JSON.stringify({
                    serverName: mcpServerName,
                    toolName: mcpToolName,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startAskTime,
                    didApprove
                  })
                });
                if (!didApprove) {
                  break;
                }
                await this.say('use_mcp_tool_result', '', true);
                const toolLog = generateToolLog('use_mcp_tool');
                toolLog.start(`${mcpServerName} ${mcpToolName} ${mcpToolParams}`);
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    serverName: mcpServerName,
                    toolName: mcpToolName,
                    arguments: mcpToolParams
                  },
                  metadata: {
                    name: block.name
                  }
                });
                // 执行
                const startToolTime = Date.now();
                const toolResult = await callMcpTool(mcpServerName, mcpToolName, mcpToolParams, this.agentTraceLogger);
                const result = JSON.stringify(
                  Array.isArray(toolResult)
                    ? toolResult?.map((item) => {
                        if (item.type === 'text') {
                          return item.text;
                        } else {
                          return '';
                        }
                      })
                    : toolResult
                );
                toolLog.end(result);
                generationCall?.end({
                  output: { result }
                });
                pushToolResult(result);
                await this.say('use_mcp_tool_result', result, false);

                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    serverName: mcpServerName,
                    toolName: mcpToolName,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: arguments
                  })
                });
                await this.saveCheckpoint();
                break;
              }
            } catch (error: any) {
              await handleError('use mcp tool', error, 'use_mcp_tool');
              break;
            }
          }
          case 'ask_followup_question': {
            const question: string | undefined = block.params.question;
            try {
              if (block.partial) {
                await this.ask('followup', removeClosingTag('question', question), block.partial).catch(() => {});
                break;
              } else {
                if (!question) {
                  this.consecutiveMistakeCount++;
                  pushToolResult(await this.sayAndCreateMissingParamError('ask_followup_question', 'question'));
                  break;
                }
                this.consecutiveMistakeCount = 0;
                this.logger.reportUserAction({
                  key: 'agent_tools_request',
                  type: 'ask_followup_question'
                });
                generationCall = this.trace?.generation({
                  name: 'tool_call',
                  input: {
                    question
                  },
                  metadata: {
                    name: block.name
                  }
                });
                const startToolTime = Date.now();
                const { text } = await this.ask('followup', question, false);
                await this.say('user_feedback', text ?? '');
                generationCall?.end({
                  output: { text }
                });
                pushToolResult(formatResponse.toolResult(`<answer>\n${text}\n</answer>`));
                this.logger.perf({
                  namespace: ASSISTANT_NAMESPACE,
                  subtag: 'kwaipilot-ide-agent-chat-tool',
                  millis: Date.now() - startToolTime,
                  extra4: 'success',
                  extra6: block.name
                });
                this.logger.reportUserAction({
                  key: 'agent_task',
                  type: 'tool_use',
                  content: JSON.stringify({
                    toolName: block.name,
                    sessionId: this.sessionId,
                    chatId: this.chatId,
                    ts: Date.now(),
                    duration: Date.now() - startToolTime,
                    params: {
                      question
                    }
                  })
                });
                break;
              }
            } catch (error: any) {
              await handleError('asking question', error, 'ask_followup_question');
              break;
            }
          }
        }
        break;
    }

    // 解锁消息处理
    // this needs to be placed here, if not then calling this.presentAssistantMessage below would fail (sometimes) since it's locked
    this.presentAssistantMessageLocked = false;
    // 处理完成块或拒绝/已使用工具的情况
    if (!block.partial || this.didRejectTool || this.didAlreadyUseTool) {
      // 检查是否是最后一个块
      if (this.currentStreamingContentIndex === this.assistantMessageContent.length - 1) {
        // its okay that we increment if !didCompleteReadingStream, it'll just return bc out of bounds and as streaming continues it will call presentAssistantMessage if a new block is ready. if streaming is finished then we set userMessageContentReady to true when out of bounds. This gracefully allows the stream to continue on and all potential content blocks be presented.
        // last block is complete and it is finished executing
        this.userMessageContentReady = true;
      }
      // 移动到下一个块
      // need to increment regardless, so when read stream calls this function again it will be streaming the next block
      this.currentStreamingContentIndex++;
      // 如果还有更多块，递归处理
      if (this.currentStreamingContentIndex < this.assistantMessageContent.length) {
        // there are already more content blocks to stream, so we'll call this function ourselves
        // await this.presentAssistantContent()
        this.presentAssistantMessage();
        return;
      }
    }
    // block is partial, but the read stream may have finished
    if (this.presentAssistantMessageHasPendingUpdates) {
      this.presentAssistantMessage();
    }
  }

  async updateAskResponse(response: { askResponse: AskResponse; text: string }) {
    this.askResponse = response.askResponse;
    this.askResponseText = response.text;
  }
  async stop() {
    this.abortController?.abort();
    this.abort = true;
    // 用户手动停止任务
    this.traceGeneration?.end({
      output: {
        msg: '用户手动停止任务'
      }
    });
  }
  async say(type: Say, text?: string, partial?: boolean, role?: 'user'): Promise<undefined> {
    if (this.abort) {
      throw new Error('Kwaipilot instance aborted');
    }
    const genSayTs = async () => {
      let sayTs = Date.now();
      if (this.lastMessageTs === sayTs) {
        await delay(1);
        sayTs = Date.now();
      }
      return sayTs;
    };
    if (partial !== undefined) {
      const lastMessage = this.localMessages.at(-1);
      const isUpdatingPreviousPartial =
        lastMessage && lastMessage.partial && lastMessage.type === 'say' && lastMessage.say === type;
      if (partial) {
        if (isUpdatingPreviousPartial) {
          // existing partial message, so update it
          lastMessage.text = text;
          lastMessage.partial = partial;
          lastMessage.role = role;
          await this.addToWebviewMessages(lastMessage);
        } else {
          // this is a new partial message, so add it with partial state
          const sayTs = await genSayTs();
          this.lastMessageTs = sayTs;
          await this.addToLocalMessages({
            ts: sayTs,
            type: 'say',
            say: type,
            text,
            partial,
            role,
            sessionId: this.sessionId,
            lastCheckpointHash: this.lastCheckpointHash
          });
        }
      } else {
        // partial=false means its a complete version of a previously partial message
        if (isUpdatingPreviousPartial) {
          // this is the complete version of a previously partial message, so replace the partial with the complete version
          this.lastMessageTs = lastMessage.ts;
          lastMessage.text = text;
          lastMessage.partial = false;
          lastMessage.role = role;
          lastMessage.lastCheckpointHash = this.lastCheckpointHash;
          this.localMessages.pop();
          // instead of streaming partialMessage events, we do a save and post like normal to persist to disk
          await this.addToLocalMessages(lastMessage);
        } else {
          // this is a new partial=false message, so add it like normal
          const sayTs = await genSayTs();
          this.lastMessageTs = sayTs;
          await this.addToLocalMessages({
            ts: sayTs,
            type: 'say',
            say: type,
            text,
            role,
            sessionId: this.sessionId,
            lastCheckpointHash: this.lastCheckpointHash
          });
        }
      }
    } else {
      // this is a new non-partial message, so add it like normal
      const sayTs = await genSayTs();
      this.lastMessageTs = sayTs;
      await this.addToLocalMessages({
        ts: sayTs,
        type: 'say',
        say: type,
        text,
        role,
        sessionId: this.sessionId,
        lastCheckpointHash: this.lastCheckpointHash
      });
    }
  }

  async ask(
    type: Ask,
    text?: string,
    partial?: boolean,
    role?: 'user'
  ): Promise<{
    type: string;
    askResponse: AskResponse;
    text?: string;
  }> {
    if (this.abort) {
      throw new Error('Kwaipilot instance aborted');
    }
    let askTs: number;
    if (partial !== undefined) {
      const lastMessage = this.localMessages.at(-1);
      const isUpdatingPreviousPartial =
        lastMessage && lastMessage.partial && lastMessage.type === 'ask' && lastMessage.ask === type;
      if (partial) {
        if (isUpdatingPreviousPartial) {
          // existing partial message, so update it
          lastMessage.text = text;
          lastMessage.partial = partial;
          lastMessage.lastCheckpointHash = this.lastCheckpointHash;
          await this.addToWebviewMessages(lastMessage);
          throw new Error('Current ask promise was ignored 1');
        } else {
          // this is a new partial message, so add it with partial state
          askTs = Date.now();
          this.lastMessageTs = askTs;
          await this.addToLocalMessages({
            ts: askTs,
            type: 'ask',
            ask: type,
            text,
            partial,
            sessionId: this.sessionId,
            lastCheckpointHash: this.lastCheckpointHash
          });
          throw new Error('Current ask promise was ignored 2');
        }
      } else {
        // partial=false means its a complete version of a previously partial message
        if (isUpdatingPreviousPartial) {
          this.askResponse = undefined;
          this.askResponseText = undefined;
          askTs = lastMessage.ts;
          this.lastMessageTs = askTs;
          lastMessage.text = text;
          lastMessage.partial = false;
          lastMessage.lastCheckpointHash = this.lastCheckpointHash;
          this.localMessages.pop();
          await this.addToLocalMessages(lastMessage);
        } else {
          // this is a new partial=false message, so add it like normal
          this.askResponse = undefined;
          this.askResponseText = undefined;
          askTs = Date.now();
          this.lastMessageTs = askTs;
          await this.addToLocalMessages({
            ts: askTs,
            type: 'ask',
            ask: type,
            text,
            sessionId: this.sessionId,
            lastCheckpointHash: this.lastCheckpointHash
          });
        }
      }
    } else {
      this.askResponse = undefined;
      this.askResponseText = undefined;
      askTs = Date.now();
      this.lastMessageTs = askTs;
      await this.addToLocalMessages({
        ts: askTs,
        type: 'ask',
        ask: type,
        text,
        sessionId: this.sessionId,
        lastCheckpointHash: this.lastCheckpointHash
      });
    }

    await pWaitFor(() => this.askResponse !== undefined, { interval: 100 });

    const result = {
      type,
      askResponse: this.askResponse!,
      text: this.askResponseText
    };
    this.askResponse = undefined;
    this.askResponseText = undefined;
    return result;
  }

  async removeLastPartialMessageIfExistsWithType(type: 'ask' | 'say', askOrSay: Ask | Say) {
    const lastMessage = this.localMessages.at(-1);
    if (
      lastMessage?.partial &&
      lastMessage.type === type &&
      (lastMessage.ask === askOrSay || lastMessage.say === askOrSay)
    ) {
      this.localMessages.pop();
      await this.saveToLocalMessages();
    }
  }

  private throttledSendSingleMessage = (() => {
    let currentSendingMessageTs: number | undefined;
    const throttled = throttle(
      async (message: LocalMessage) => {
        await this.messenger.send('assistant/agent/message', message);
      },
      200,
      {
        leading: true,
        trailing: true
      }
    );
    return (message: LocalMessage) => {
      if (currentSendingMessageTs && message.ts !== currentSendingMessageTs) {
        throttled.flush();
      }
      currentSendingMessageTs = message.ts;
      return throttled(message);
    };
  })();

  private async addToWebviewMessages(message: LocalMessage) {
    message.chatId = this.chatId;
    message.sessionId = this.sessionId;
    this.agentTraceLogger.debug(`发送给ide的消息：${JSON.stringify(message)}`);
    this.throttledSendSingleMessage(message);
  }

  private async addToLocalMessages(message: LocalMessage) {
    message.chatId = this.chatId;
    this.localMessages.push(message);
    await this.saveToLocalMessages();
    this.agentTraceLogger.debug(`发送给ide的消息：${JSON.stringify(message)}`);
    this.throttledSendSingleMessage(message);
  }

  private async saveToLocalMessages() {
    this.agentTraceLogger.debug(`发送给ide的messageList消息：${JSON.stringify(this.localMessages)}`);
    await this.messenger.send('assistant/agent/messageList', this.localMessages);
  }

  private async addToApiConversationHistory(message: Omit<MessageParamVersion1, 'version'>, isNewTask = false) {
    message.chatId = this.chatId;
    this.apiConversationHistory.push({
      ...message,
      version: 1
    });
    await this.messenger.send('assistant/agent/apiConversationList', this.apiConversationHistory);
  }

  async getEnvironmentDetails(includeFileDetails: boolean = false) {
    const res = await this.messenger.request('assistant/agent/environment', { includeFileDetails });
    return res.data || '';
  }

  async sayAndCreateMissingParamError(toolName: ToolUseName, paramName: string, relPath?: string) {
    await this.say('tool_error', `尝试使用 ${toolName}， 因缺少参数'${paramName}'调用失败. 正在重试中...`);
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - this.startLlmTime,
      extra4: 'error',
      extra6: toolName
    });
    return formatResponse.toolError(formatResponse.missingToolParameterError(paramName));
  }

  async handleCompletedTask() {
    await this.say('completion_result', '', false);
    this.didCompleteTask = true;
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-all',
      millis: Date.now() - this.startTaskTime,
      extra4: `success`
    });
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-agent-total',
      millis: Date.now() - this.startTaskTime,
      extra4: this.modelConfig.model
    });
    this.logger.reportUserAction({
      key: 'agent_task',
      type: 'agent_task_end',
      content: JSON.stringify({
        sessionId: this.sessionId,
        chatId: this.chatId,
        ts: Date.now(),
        duration: Date.now() - this.startTaskTime,
        modelName: this.modelConfig.model
      })
    });
  }

  async executeCommandTool(
    command: string,
    is_background?: boolean
  ): Promise<{ userRejected: boolean; result: string; cutRes: string }> {
    const commandRes = await this.messenger.request('assistant/agent/executeCommand', { command, is_background });
    const { userRejected, result, completed = true } = commandRes.data as ExecuteCommandResponse;

    const resultArray = result.split('\n');
    let cutRes = resultArray[0];
    for (let i = 1; i < resultArray.length; i++) {
      const newContent = cutRes + `\n${resultArray[i]}`;
      if (newContent.length >= 500) {
        break;
      } else {
        cutRes = newContent;
      }
    }
    cutRes = cutRes.slice(0, 500);
    return {
      userRejected,
      result,
      cutRes: completed
        ? `Command executed.${cutRes.length > 0 ? `\nOutput:\n${cutRes}` : ''}`
        : `Command is still running in the user's terminal.${
            cutRes.length > 0 ? `\nHere's the output so far:\n${cutRes}` : ''
          }\n\nYou will be updated on the terminal status and new output in the future.`
    };
  }

  async onEditFile(request: EditFileRequest): Promise<EditFileResponse> {
    const res = await this.messenger.request('assistant/agent/editFile', request);
    return res.data || { type: 'failed', content: '未返回文件修改结果' };
  }
  async saveCheckpoint() {
    const startTime = Date.now();
    const traceCheckpointCreate = this.trace?.generation({
      name: 'checkpoint_created'
    });
    if (!this.checkpointTracker) {
      // await this.say('error', '检查点初始化失败，无法创建检查点');
      return;
    }
    try {
      const commitHash = await this.checkpointTracker?.commit();
      if (!commitHash) return;
      this.lastCheckpointHash = commitHash;
      await this.say('checkpoint_created', commitHash);
      traceCheckpointCreate?.end({
        output: { commitHash }
      });
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-saveCheckpoint',
        millis: Date.now() - startTime,
        extra4: `success`
      });
    } catch (error) {
      // await this.say('error', '检查点创建失败，无法创建检查点');
      traceCheckpointCreate?.end({
        output: { error: 'Failed to create checkpoint: ' + error }
      });
      this.logger.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-saveCheckpoint',
        millis: Date.now() - startTime,
        extra4: `error`
      });
    }
  }

  async initCheckpointTracker() {
    const startTime = Date.now();
    if (!this.checkpointTracker && !this.checkpointTrackerErrorMessage) {
      const type = this.restoreInfo?.type || 'newTask';
      const traceCheckpointInit = this.trace?.generation({
        name: `checkpoint_init_${type}`,
        input: {
          sessionId: this.sessionId,
          cwd: this.cwd
        }
      });
      try {
        this.checkpointTracker = await pTimeout(CheckpointTracker.create(this.sessionId, this.cwd), {
          milliseconds: 15_000,
          message:
            'Checkpoints taking too long to initialize. Consider re-opening Kwaipilot in a project that uses git, or disabling checkpoints.'
        });
        traceCheckpointInit?.end({
          output: { init: 'success' }
        });
        this.logger.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-initCheckpoint',
          millis: Date.now() - startTime,
          extra4: `success`
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Failed to initialize checkpoint tracker:', errorMessage);
        traceCheckpointInit?.end({
          output: { error: 'Failed to initialize checkpoint tracker: ' + errorMessage }
        });
        this.logger.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-initCheckpoint',
          millis: Date.now() - startTime,
          extra4: `error`
        });
        this.checkpointTrackerErrorMessage = errorMessage; // will be displayed right away since we saveClineMessages next which posts state to webview
      }
    }
  }
  async checkpointResetHead(commitHash: string) {
    this.trace = getTrace(this.sessionId, this.sessionInfo?.reqData.username, {
      sessionId: this.sessionId,
      metadata: {
        action: 'restore'
      },
      input: { commitHash }
    });
    // 先初始化检查点跟踪器
    await this.initCheckpointTracker();

    const traceCheckpointReset = this.trace?.generation({
      name: 'checkpoint_restore',
      input: {
        sessionId: this.sessionId,
        cwd: this.cwd,
        commitHash
      }
    });
    if (commitHash && this.checkpointTracker) {
      try {
        await this.checkpointTracker.resetHead(commitHash);
        // this.say('checkpoint_restore_success', commitHash);
        traceCheckpointReset?.end({
          output: { status: 'success' }
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.addToWebviewMessages({
          ts: Date.now(),
          type: 'say',
          say: 'error',
          text: errorMessage,
          sessionId: this.sessionId,
          chatId: this.chatId
        });
        traceCheckpointReset?.end({
          output: { status: 'failed', error: errorMessage }
        });
      }
    }
  }
}

/**
 * 从API获取模型配置
 * @returns 返回从API获取的模型配置
 */
async function fetchModelConfig(data: {
  modelId?: string;
  username: string;
  preference?: string;
}): Promise<{ model: string; maxTokens: number }> {
  const httpClient = new Api();
  // 调用API获取模型配置
  const response = await httpClient.get<{ name: string; maxInputTokens: number }>(
    `/eapi/kwaipilot/plugin/model-config?username=${data.username}${data.preference ? `&mode=${data.preference}` : ''}${
      data.modelId ? `&model=${data.modelId}` : ''
    }`,
    {},
    {
      signal: AbortSignal.timeout(3000)
    }
  );

  return { model: response.data.name, maxTokens: response.data.maxInputTokens };
}

/**
 * 从kconf获取白名单配置
 * @returns 返回从API获取的模型配置
 */
async function fetchGrayConfig(data: { grayKey: string; username: string }): Promise<boolean> {
  const { grayKey, username } = data;
  const httpClient = new Api();
  // 调用API获取模型配置
  const response = await httpClient.get<{ full: boolean; name: string; whitelist: string[] }[]>(
    `eapi/kwaipilot/plugin/v2/config?key=kwaipilot.platform.user_gray_config`
  );

  // 是否包含当前用户
  const targetConfig = response.data.find((item) => item.name === grayKey);
  if (!targetConfig) {
    return false;
  }
  if (targetConfig.full) {
    return true;
  }
  if (targetConfig.whitelist.includes(username)) {
    return true;
  }
  return false;
}
