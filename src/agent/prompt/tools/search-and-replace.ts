import { ToolArgs } from "./types"

export function getSearchAndReplaceDescription(args: ToolArgs): string {
	return `## search_and_replace
Description: Use this tool to find and replace specific text strings or patterns (using regex) within a file. It's suitable for targeted replacements across multiple locations within the file. Supports literal text and regex patterns, case sensitivity options, and optional line ranges.
Parameters:
- path: (required)The path of the file to modify (relative to the current workspace directory ${args.cwd.toPosix()})
- replacements: (required)A JSON array of objects representing a list of replacements, each containing:

  - search: (required)The text to search for, it should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
  - replace: (required)The text to replace matches with, it should contain the edited lines that should replace the search. Can be an empty string to delete content
  - start_line: (optional)Starting line number for restricted replacement (1-based)
  - end_line: (optional)Ending line number for restricted replacement (1-based)

Notes:
- This is the only tool you should use for editing files.
- DO NOT fall back to removing the whole file and recreating it from scratch.
- Try to fit as many edits in one tool call as possible`;
}

export function getSearchAndReplaceExample(): string {
  return `
<search_and_replace>
<path>example.ts</path>
<replacements>
[
  {
    "search": "oldText",
    "replace": "newText",
    "start_line": 1,
    "end_line": 10
  },
  {
    "search": "oldText2",
    "replace": "newText2",
    "start_line": 50,
    "end_line": 100
  }
]
</replacements>
</search_and_replace>`;
}
