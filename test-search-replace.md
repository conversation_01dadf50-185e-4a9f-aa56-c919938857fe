# Search and Replace Tool 重构测试

## 重构总结

已成功重构 `searchAndReplaceTool` 以支持新的工具定义：

### 主要改进

1. **支持批量替换操作**
   - 新增 `replacements` 参数，支持 JSON 数组格式
   - 每个替换操作包含：`search`, `replace`, `start_line`, `end_line`

2. **解决行号冲突问题**
   - 实现智能排序：按行号倒序处理替换操作
   - 全局替换（无行号限制）最后处理
   - 避免前面的替换影响后面的行号

3. **向后兼容性**
   - 保持对原有 `search`/`replace` 参数的支持
   - 自动转换为单个操作格式

4. **增强验证**
   - 重叠行号范围检测
   - JSON 格式验证
   - 参数完整性检查

### 新的工具使用方式

#### 方式1：批量替换（推荐）
```xml
<search_and_replace>
<path>example.ts</path>
<replacements>
[
  {
    "search": "oldFunction1",
    "replace": "newFunction1",
    "start_line": 10,
    "end_line": 20
  },
  {
    "search": "oldFunction2", 
    "replace": "newFunction2",
    "start_line": 50,
    "end_line": 60
  }
]
</replacements>
</search_and_replace>
```

#### 方式2：单个替换（向后兼容）
```xml
<search_and_replace>
<path>example.ts</path>
<search>oldText</search>
<replace>newText</replace>
<start_line>10</start_line>
<end_line>20</end_line>
</search_and_replace>
```

### 处理顺序

1. **验证参数** - 检查必需参数和格式
2. **重叠检测** - 确保行号范围不重叠
3. **智能排序** - 按行号倒序排列操作
4. **批量执行** - 从文件底部向顶部依次替换

### 技术细节

- **排序算法**：优先处理有行号限制的操作，按 start_line 倒序
- **重叠检测**：O(n²) 算法检测所有操作对的行号范围重叠
- **错误处理**：详细的错误信息和位置提示
- **性能优化**：单次文件读写，内存中批量处理

这个重构解决了您提到的核心问题：避免串行替换导致的行号偏移，确保批量替换操作的准确性。
