const fs = require('fs');
const { execSync } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const packageJson = require('../../package.json');
const rimrafSync = require('rimraf').sync;
const path = require('path');

function execCmdSync(cmd) {
  try {
    execSync(cmd);
  } catch (err) {
    console.error(`Error executing command '${cmd}': `, err.output.toString());
    process.exit(1);
  }
}

function autodetectPlatformAndArch() {
  platform = {
    aix: 'linux',
    alpine: 'linux',
    darwin: 'darwin',
    freebsd: 'linux',
    linux: 'linux',
    openbsd: 'linux',
    sunos: 'linux',
    win32: 'win32'
  }[process.platform];
  arch = {
    arm: 'arm64',
    armhf: 'arm64',
    arm64: 'arm64',
    ia32: 'x64',
    loong64: 'arm64',
    mips: 'arm64',
    mipsel: 'arm64',
    ppc: 'x64',
    ppc64: 'x64',
    riscv64: 'arm64',
    s390: 'x64',
    s390x: 'x64',
    x64: 'x64'
  }[process.arch];
  return [platform, arch];
}

function validateFilesPresent(pathsToVerify) {
  // This script verifies after pacakging that necessary files are in the correct locations
  // In many cases just taking a sample file from the folder when they are all roughly the same thing

  let missingFiles = [];
  let emptyFiles = [];
  for (const path of pathsToVerify) {
    if (!fs.existsSync(path)) {
      const parentFolder = path.split('/').slice(0, -1).join('/');
      const grandparentFolder = path.split('/').slice(0, -2).join('/');
      const grandGrandparentFolder = path.split('/').slice(0, -3).join('/');

      console.error(`File ${path} does not exist`);
      if (!fs.existsSync(parentFolder)) {
        console.error(`Parent folder ${parentFolder} does not exist`);
      } else {
        console.error('Contents of parent folder:', fs.readdirSync(parentFolder));
      }
      if (!fs.existsSync(grandparentFolder)) {
        console.error(`Grandparent folder ${grandparentFolder} does not exist`);
        if (!fs.existsSync(grandGrandparentFolder)) {
          console.error(`Grandgrandparent folder ${grandGrandparentFolder} does not exist`);
        } else {
          console.error('Contents of grandgrandparent folder:', fs.readdirSync(grandGrandparentFolder));
        }
      } else {
        console.error('Contents of grandparent folder:', fs.readdirSync(grandparentFolder));
      }

      missingFiles.push(path);
    }

    if (fs.existsSync(path) && fs.statSync(path).size === 0) {
      console.error(`File ${path} is empty`);
      emptyFiles.push(path);
    }
  }

  if (missingFiles.length > 0 || emptyFiles.length > 0) {
    throw new Error(
      `The following files were missing:\n- ${missingFiles.join(
        '\n- '
      )}\n\nThe following files were empty:\n- ${emptyFiles.join('\n- ')}`
    );
  } else {
    console.log('All paths exist');
  }
}
// Upload the archive to KCDN
const uploadFile = async (filepath) => {
  console.log(`[info] Uploading file: ${filepath}`);

  // Read configuration from environment variables or config file
  const config = {
    CDN_TOKEN: '111992_fa32806738399acc12caf59c847363ab',
    CDN_PID: 'kwaipilot',
    CDN_UPLOAD_SINGLE_URL:
      'https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/single?token=111992_fa32806738399acc12caf59c847363ab'
  };

  if (!config.CDN_UPLOAD_SINGLE_URL || !config.CDN_PID) {
    console.error('[error] Missing CDN configuration. Set CDN_UPLOAD_SINGLE_URL and CDN_PID environment variables.');
    return;
  }

  const fileContent = fs.readFileSync(filepath);

  const filename = `/kwaipilot-binary/${path.basename(filepath)}`;

  // Create form data
  // Create form data
  const formData = new FormData();
  formData.append('file', fileContent, path.basename(filepath));
  formData.append('allowHash', 'false');
  formData.append('pid', config.CDN_PID);
  formData.append('filename', filename);
  formData.append('allowRewrite', 'false');
  formData.append('allowMD5', 'false');
  formData.append('requestInfo.uploaderType', '2');
  formData.append('requestInfo.serviceName', config.CDN_PID);

  try {
    const response = await axios.post(config.CDN_UPLOAD_SINGLE_URL, formData, {
      headers: formData.getHeaders()
    });

    console.log(`[info] Upload response:`, response.data);
    return response.data;
  } catch (error) {
    console.error('[error] Upload failed:', error.message);
    if (error.response) {
      console.error('[error] Response data:', error.response.data);
    }
    throw error;
  }
};

function renameBinary(dist) {
  // Rename dist/bin directory to dist/kwaipilot-binary
  console.log('[info] Renaming dist/bin to dist/kwaipilot-binary...');
  const oldPath = path.join(dist, 'bin');
  const newPath = path.join(dist, 'kwaipilot-binary');

  if (fs.existsSync(oldPath)) {
    if (fs.existsSync(newPath)) {
      rimrafSync(newPath);
    }
    fs.renameSync(oldPath, newPath);
    console.log('[info] Successfully renamed directory.');
  } else {
    console.warn('[warn] Could not rename directory: dist/bin does not exist.');
  }
}

function uploadBinary(dist) {
  // Create a tar.gz archive of the kwaipilot-binary directory
  renameBinary(dist);
  // If dist is not provided, use the default dist directory
  if (!dist) {
    dist = path.join(__dirname, '..', '..', 'dist');
    console.log(`[info] No dist path provided, using default: ${dist}`);
  }

  // Get version from package.json
  const version = packageJson.version;
  const archiveName = `kwaipilot-binary-${version}.tar.gz`;
  const sourceCodeName = `kwaipilot-source-code-${version}.tar.gz`;
  const archivePath = path.join(dist, archiveName);
  const sourceCodePath = path.join(dist, sourceCodeName);
  try {
    // 上传 binary 文件
    // Create tar.gz archive
    execCmdSync(`cd ${dist} && tar -czf ${archiveName} kwaipilot-binary`);
    console.log(`[info] Created archive: ${archivePath}`);

    // Execute the upload
    uploadFile(archivePath).catch((error) => {
      console.error('[error] Failed to upload archive:', error);
    });

    // 上传 dist 目录，方便以后排查问题的时候可以找到对应的源码。
    execCmdSync(`cd ${dist} && tar -czf ${sourceCodeName} out`);
    console.log(`[info] Created source code archive: ${sourceCodePath}`);

    uploadFile(sourceCodePath).catch((error) => {
      console.error('[error] Failed to upload source code archive:', error);
    });
  } catch (error) {
    console.error('[error] Failed to package or upload:', error);
  }
}

async function downloadRipgrepBinary(target, targetDir) {
  console.log('[info] Downloading pre-built ripgrep binary');
  rimrafSync('node_modules/@vscode/ripgrep/bin');
  fs.mkdirSync('node_modules/@vscode/ripgrep/bin', { recursive: true });
  4;
  const downloadUrl = {
    'darwin-arm64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-aarch64-apple-darwin.tar.gz',
    'linux-arm64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-aarch64-unknown-linux-gnu.tar.gz',
    'win32-arm64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-aarch64-pc-windows-msvc.zip',
    'linux-x64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-x86_64-unknown-linux-musl.tar.gz',
    'darwin-x64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-x86_64-apple-darwin.tar.gz',
    'win32-x64':
      'https://github.com/microsoft/ripgrep-prebuilt/releases/download/v13.0.0-10/ripgrep-v13.0.0-10-x86_64-pc-windows-msvc.zip'
  }[target];

  if (target.startsWith('win')) {
    execCmdSync(`curl -L -o node_modules/@vscode/ripgrep/bin/build.zip ${downloadUrl}`);
    execCmdSync('cd node_modules/@vscode/ripgrep/bin && unzip build.zip');
    fs.unlinkSync('node_modules/@vscode/ripgrep/bin/build.zip');
  } else {
    execCmdSync(`curl -L -o node_modules/@vscode/ripgrep/bin/build.tar.gz ${downloadUrl}`);
    execCmdSync('cd node_modules/@vscode/ripgrep/bin && tar -xvzf build.tar.gz');
    fs.unlinkSync('node_modules/@vscode/ripgrep/bin/build.tar.gz');
  }
  // await copy repgrep();
  fs.mkdirSync(`${targetDir}/bin`, { recursive: true });
  fs.copyFileSync(
    `node_modules/@vscode/ripgrep/bin/rg${target.startsWith('win') ? '.exe' : ''}`,
    `${targetDir}/bin/rg${target.startsWith('win') ? '.exe' : ''}`
  );
}
module.exports = {
  execCmdSync,
  validateFilesPresent,
  autodetectPlatformAndArch,
  uploadBinary,
  downloadRipgrepBinary
};
